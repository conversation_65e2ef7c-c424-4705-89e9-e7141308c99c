using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using FairyGUI;

/// <summary>
/// 智能层级管理器，负责管理所有拼块和thickness的层级
/// </summary>
public class SmartLayerManager
{
    private GComponent rootContainer;
    private JigsawPanel parentPanel;
    private LayerContainerManager containerManager;
    private List<JigsawPiece> pieces = new List<JigsawPiece>();

    
    public SmartLayerManager(GComponent container, JigsawPanel panel)
    {
        rootContainer = container;
        parentPanel = panel;
        containerManager = new LayerContainerManager(container);
    }

    /// <summary>
    /// 确保拼块有thickness
    /// </summary>
    /// <param name="piece">拼块</param>
    private void EnsurePieceHasThickness(JigsawPiece piece)
    {
        if (piece == null) return;
        
        // 检查是否已经有thickness
        var existingThickness = GetThicknessFor(piece);
        if (existingThickness != null) return;
        
        // 创建新的thickness
        try
        {
            if (rootContainer == null)
            {
                UnityEngine.Debug.LogError("SmartLayerManager: rootContainer is null!");
                return;
            }
            
            var thicknessClone = FairyGUI.UIPackage.CreateObject("Jigsaw", "JigsawThickness").asCom;
            var thickness = thicknessClone.GetChild("thickness").asLoader;
            
            var url = $"ui://Z_Image_{piece.imageIndex}/piece_{piece.pieceIndex}";
            thickness.url = url;
            
            // 将thickness添加到对应的容器（默认为普通thickness容器）
            var thicknessContainer = containerManager.GetThicknessContainer(false, false);
            thicknessContainer.AddChild(thicknessClone);
            
            // 隐藏拼块自身的thickness
            piece.SetThicknessVisible(false);
            
            // 建立映射关系
            StorePieceThicknessMapping(piece, thicknessClone);
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"Failed to create thickness for piece {piece.pieceIndex}: {ex.Message}");
        }
    }
    
    // 映射存储
    private Dictionary<JigsawPiece, GComponent> pieceThicknessMap = new Dictionary<JigsawPiece, GComponent>();
    
    /// <summary>
    /// 存储拼块thickness映射
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    private void StorePieceThicknessMapping(JigsawPiece piece, GComponent thickness)
    {
        pieceThicknessMap[piece] = thickness;
    }

    /// <summary>
    /// 从层级管理系统移除拼块
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UnregisterPiece(JigsawPiece piece)
    {
        // todo 重新定义
        RemovePieceThickness(piece);
    }
    
    /// <summary>
    /// 移除拼块的thickness
    /// </summary>
    /// <param name="piece">拼块</param>
    private void RemovePieceThickness(JigsawPiece piece)
    {
        if (piece == null) return;
        
        if (pieceThicknessMap.TryGetValue(piece, out GComponent thickness))
        {
            // 从当前容器中移除,todo 复用thickness
            if (thickness.parent != null)
            {
                thickness.parent.RemoveChild(thickness, true);
            }

            // 移除映射关系
            pieceThicknessMap.Remove(piece);

            // 显示拼块自身的thickness
            piece.SetThicknessVisible(true);
        }
    }
    
    /// <summary>
    /// 更新拼块的层级
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UpdatePieceLayer(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed) return;

        var thickness = GetThicknessFor(piece);
        try
        {
            // 检查同一格子上是否已有其他拼块
            var piecesAtSameGrid = GetPiecesAtSameGrid(piece);
            bool hasOtherPiecesAtSameGrid = piecesAtSameGrid.Count > 1; // 大于1表示除了当前拼块还有其他拼块

            // 如果同一格子上已有其他拼块，将这些拼块移动到OVERLAY层
            if (hasOtherPiecesAtSameGrid)
            {
                MovePiecesToOverlayLayer(piecesAtSameGrid);
                return; // 已经在MovePiecesToOverlayLayer中处理了当前拼块的层级
            }

            // 如果没有其他拼块在同一格子，使用普通层级
            var targetPieceContainer = containerManager.GetPieceContainer(false, false);
            var targetThicknessContainer = containerManager.GetThicknessContainer(false, false);

            // 移动piece到正确的容器
            if (piece.parent != targetPieceContainer && !piece.isDisposed)
            {
                containerManager.MoveToContainer(piece, targetPieceContainer);
            }

            // 移动thickness到正确的容器
            if (thickness != null && thickness.parent != targetThicknessContainer && !thickness.isDisposed)
            {
                containerManager.MoveToContainer(thickness, targetThicknessContainer);
            }

            // 更新thickness的位置坐标
            if (thickness != null && !thickness.isDisposed)
            {
                UpdateThicknessPosition(piece, thickness);
            }
        }
        catch (System.Exception ex)
        {
            // 如果设置层级失败，记录错误但继续执行
            UnityEngine.Debug.LogError($"Failed to update piece layer: {ex.Message}");
        }
    }


    
    /// <summary>
    /// 更新thickness的位置坐标
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    private void UpdateThicknessPosition(JigsawPiece piece, GComponent thickness)
    {
        if (piece == null || thickness == null || piece.isDisposed) return;
        
        try
        {
            // 获取拼块的全局位置
            Vector2 pieceGlobalPos = piece.LocalToGlobal(Vector2.zero);
            
            // 转换为thickness父容器的本地坐标
            if (thickness.parent != null)
            {
                Vector2 thicknessLocalPos = thickness.parent.GlobalToLocal(pieceGlobalPos);
                thickness.SetXY(thicknessLocalPos.x, thicknessLocalPos.y);
            }
        }
        catch (System.Exception)
        {
            // 忽略坐标转换错误
        }
    }

    public void AddPiece(JigsawPiece piece)
    {
        pieces.Add(piece);
    }
    
    /// <summary>
    /// 开始拖拽
    /// </summary>
    /// <param name="piece">拼块</param>
    public void StartDragging(JigsawPiece piece)
    {
        // 如果是组拖拽，同时处理组内其他拼块
        if (piece.GetGroup() != null)
        {
            foreach (var groupPiece in piece.GetGroup().Pieces)
            {
                EnsurePieceHasThickness(groupPiece);
                ForceDraggingLayerUpdate(groupPiece);
            }
        }
        else
        {
            EnsurePieceHasThickness(piece);
            ForceDraggingLayerUpdate(piece);
        }
    }
    
    /// <summary>
    /// 强制更新拼块到拖拽层级
    /// </summary>
    /// <param name="piece">拼块</param>
    private void ForceDraggingLayerUpdate(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed) return;
        var thickness = GetThicknessFor(piece);

        try
        {
            // 获取拖拽层级的容器
            var draggingPieceContainer = containerManager.GetPieceContainer(true, false);
            var draggingThicknessContainer = containerManager.GetThicknessContainer(true, false);

            // 移动thickness到拖拽容器
            if (thickness != null && thickness.parent != draggingThicknessContainer && !thickness.isDisposed)
            {
                containerManager.MoveToContainer(thickness, draggingThicknessContainer);
                UpdateThicknessPosition(piece, thickness);
            }

            // 移动piece到拖拽容器
            if (piece.parent != draggingPieceContainer && !piece.isDisposed)
            {
                containerManager.MoveToContainer(piece, draggingPieceContainer);
            }
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"Failed to force dragging layer update: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 停止拖拽
    /// </summary>
    /// <param name="piece">拼块</param>
    public void StopDragging(JigsawPiece piece)
    {
        StopDragging(piece, true);
    }
    
    /// <summary>
    /// 停止拖拽（内部方法，支持防止递归）
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="processGroup">是否处理组内其他拼块</param>
    private void StopDragging(JigsawPiece piece, bool processGroup)
    {
        UpdatePieceLayer(piece);
        
        // 如果是组拖拽且需要处理组，同时处理组内其他拼块（防止递归）
        if (processGroup && piece.GetGroup() != null)
        {
            foreach (var groupPiece in piece.GetGroup().Pieces)
            {
                if (groupPiece != piece)
                {
                    StopDragging(groupPiece, false); // 不再处理组，防止递归
                }
            }
        }
    }

    /// <summary>
    /// 更新拼块位置
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UpdatePiecePosition(JigsawPiece piece)
    {
        var thickness = GetThicknessFor(piece);
        if (thickness == null) return;
        UpdateThicknessPositionOnly(piece, thickness);

        // if (!pieceLayerInfo.ContainsKey(piece)) return;

        // var info = pieceLayerInfo[piece];
        // Vector2Int newGridPos = CalculateGridPosition(piece);

        // // 如果网格位置发生变化，更新相关信息
        // if (newGridPos != info.gridPosition)
        // {
        //     gridManager.RemovePieceFromGrid(info.gridPosition, piece);
        //     gridManager.AddPieceToGrid(newGridPos, piece);

        //     // 保留原有的拖拽状态，只更新位置相关信息
        //     bool originalDragging = info.isDragging;

        //     info.gridPosition = newGridPos;
        //     info.stackIndex = gridManager.GetStackIndex(newGridPos, piece);
        //     info.isOverlay = gridManager.IsOverlayPosition(newGridPos, piece);
        //     info.isDragging = originalDragging; // 保持原有拖拽状态

        //     pieceLayerInfo[piece] = info;
        //     UpdatePieceLayer(piece);
        // }
    }
    
    /// <summary>
    /// 计算拼块的网格位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>网格位置</returns>
    private Vector2Int CalculateGridPosition(JigsawPiece piece)
    {
        if (parentPanel == null || piece == null) return Vector2Int.zero;

        // 安全检查：确保拼块有有效的父容器
        if (piece.parent == null || piece.isDisposed) return Vector2Int.zero;

        try
        {
            Vector2 centerOffset = new Vector2(piece.width * 0.5f, piece.height * 0.5f);
            Vector2 globalCenterPos = piece.LocalToGlobal(centerOffset);
            Vector2 operationLayerLocalPos = parentPanel.GlobalToOperationLayerLocal(globalCenterPos);

            return parentPanel.GetGridPosition(operationLayerLocalPos);
        }
        catch (System.Exception)
        {
            // 如果转换失败，返回默认值
            return Vector2Int.zero;
        }
    }

    /// <summary>
    /// 获取与指定拼块在同一格子位置的所有拼块
    /// </summary>
    /// <param name="targetPiece">目标拼块</param>
    /// <returns>同一格子位置的拼块列表</returns>
    private List<JigsawPiece> GetPiecesAtSameGrid(JigsawPiece targetPiece)
    {
        var result = new List<JigsawPiece>();
        if (targetPiece == null || targetPiece.isDisposed) return result;

        Vector2Int targetGridPos = CalculateGridPosition(targetPiece);

        // 遍历所有已注册的拼块，找出在同一格子位置的拼块
        foreach (var piece in pieces)
        {
            if (piece == null || piece.isDisposed) continue;

            Vector2Int pieceGridPos = CalculateGridPosition(piece);
            if (pieceGridPos == targetGridPos)
            {
                result.Add(piece);
            }
        }

        return result;
    }

    /// <summary>
    /// 将拼块列表移动到OVERLAY层，如果拼块在组内，整个组都会被移动
    /// </summary>
    /// <param name="piecesToMove">要移动的拼块列表</param>
    private void MovePiecesToOverlayLayer(List<JigsawPiece> piecesToMove)
    {
        if (piecesToMove == null || piecesToMove.Count == 0) return;

        var processedGroups = new HashSet<JigsawGroup>();
        var allPiecesToMove = new HashSet<JigsawPiece>();

        // 收集所有需要移动的拼块（包括组内的其他拼块）
        foreach (var piece in piecesToMove)
        {
            if (piece == null || piece.isDisposed) continue;

            var group = piece.GetGroup();
            if (group != null && !processedGroups.Contains(group))
            {
                // 如果拼块属于组，添加整个组的所有拼块
                processedGroups.Add(group);
                foreach (var groupPiece in group.Pieces)
                {
                    if (groupPiece != null && !groupPiece.isDisposed)
                    {
                        allPiecesToMove.Add(groupPiece);
                    }
                }
            }
            else if (group == null)
            {
                // 如果拼块不属于任何组，直接添加
                allPiecesToMove.Add(piece);
            }
        }

        // 将所有收集到的拼块移动到OVERLAY层
        foreach (var piece in allPiecesToMove)
        {
            MovePieceToOverlayLayer(piece);
        }
    }

    /// <summary>
    /// 将单个拼块移动到OVERLAY层
    /// </summary>
    /// <param name="piece">要移动的拼块</param>
    private void MovePieceToOverlayLayer(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed) return;

        var thickness = GetThicknessFor(piece);
        try
        {
            // 获取OVERLAY层的容器
            var overlayPieceContainer = containerManager.GetPieceContainer(false, true);
            var overlayThicknessContainer = containerManager.GetThicknessContainer(false, true);

            // 移动piece到OVERLAY容器
            if (piece.parent != overlayPieceContainer && !piece.isDisposed)
            {
                containerManager.MoveToContainer(piece, overlayPieceContainer);
            }

            // 移动thickness到OVERLAY容器
            if (thickness != null && thickness.parent != overlayThicknessContainer && !thickness.isDisposed)
            {
                containerManager.MoveToContainer(thickness, overlayThicknessContainer);
                UpdateThicknessPosition(piece, thickness);
            }
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"Failed to move piece to overlay layer: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 获取拼块对应的thickness
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>thickness组件</returns>
    public GComponent GetThicknessFor(JigsawPiece piece)
    {
        if (piece == null) return null;
        
        if (pieceThicknessMap.TryGetValue(piece, out GComponent thickness))
        {
            return thickness;
        }
        
        return null;
    }
    
    /// <summary>
    /// 仅更新thickness位置，不触碰层级逻辑
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    public void UpdateThicknessPositionOnly(JigsawPiece piece, GComponent thickness)
    {
        if (piece == null || thickness == null || piece.isDisposed) return;
        
        try
        {
            // 获取拼块的全局位置
            Vector2 pieceGlobalPos = piece.LocalToGlobal(Vector2.zero);
            
            // 转换为thickness父容器的本地坐标
            if (thickness.parent != null)
            {
                Vector2 thicknessLocalPos = thickness.parent.GlobalToLocal(pieceGlobalPos);
                thickness.SetXY(thicknessLocalPos.x, thicknessLocalPos.y);
            }
        }
        catch (System.Exception)
        {
            // 忽略坐标转换错误
        }
    }
    
    /// <summary>
    /// 获取所有已注册的拼块
    /// </summary>
    /// <returns>拼块列表</returns>
    public List<JigsawPiece> GetAllRegisteredPieces()
    {
        return pieces;
    }
    
    /// <summary>
    /// 清空所有层级信息
    /// </summary>
    public void Clear()
    {
        // 清理所有thickness
        foreach (var piece in pieceThicknessMap.Keys.ToList())
        {
            RemovePieceThickness(piece);
        }

        pieces.Clear();
        pieceThicknessMap.Clear();

        // 清理容器管理器
        containerManager?.Clear();
    }

    /// <summary>
    /// 销毁层级管理器
    /// </summary>
    public void Dispose()
    {
        Clear();
        containerManager?.Dispose();
        containerManager = null;
    }
}